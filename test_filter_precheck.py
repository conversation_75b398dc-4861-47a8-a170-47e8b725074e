#!/usr/bin/env python3
"""
滤池预检查功能测试脚本

测试新增的视觉大模型预判断功能，验证是否能正确识别不满足分析条件的图像
"""

import sys
import os
import cv2
import logging
from pathlib import Path

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from server.utils.handlers.filter_handler import FilterHandler
from server.utils.logger import setup_logging

# 配置日志
setup_logging()
logger = logging.getLogger(__name__)

class MockProcessor:
    """模拟主处理器类，用于测试"""
    
    def __init__(self):
        self.yolo_model_filter = "llms/models/yolo/best-filter.pt"
        self.save_filter_result = True
        self._last_annotated_image = None
    
    def _process_image_comparison(self, frame, frame_path, standard_image_path, system_type):
        """模拟图像比较方法"""
        return {
            "你的思考": "这是一个测试响应",
            "曝气头是否脱落或损坏": "否"
        }
    
    def _get_last_annotated_image(self):
        """模拟获取标注图像"""
        return self._last_annotated_image
    
    def _determine_alarm_status(self, coverage_float, threshold):
        """模拟警报状态判断"""
        is_abnormal = coverage_float > threshold
        alarm_status = 'WARNING' if is_abnormal else 'NO_ALARM'
        return (alarm_status, is_abnormal)

def test_filter_precheck():
    """测试滤池预检查功能"""
    
    # 创建模拟处理器和滤池处理器
    mock_processor = MockProcessor()
    filter_handler = FilterHandler(mock_processor)
    
    # 测试图片路径列表（您可以根据实际情况修改这些路径）
    test_images = [
        "assets/frame_4010_2024_12_06_17_48_30.jpg",  # 有水面的图片
        "assets/5fdcf4d20eaadc93ff7258a968800c0a.png",  # 可能没有水面的图片
        # 您可以添加更多测试图片
    ]
    
    for image_path in test_images:
        if not os.path.exists(image_path):
            logger.warning(f"测试图片不存在，跳过: {image_path}")
            continue
            
        logger.info(f"\n{'='*60}")
        logger.info(f"测试图片: {image_path}")
        logger.info(f"{'='*60}")
        
        try:
            # 读取测试图片
            frame = cv2.imread(image_path)
            if frame is None:
                logger.error(f"无法读取图片: {image_path}")
                continue
            
            # 测试预检查功能
            is_suitable, description = filter_handler._check_filter_analysis_conditions(frame)
            
            logger.info(f"预检查结果:")
            logger.info(f"  - 是否满足条件: {is_suitable}")
            logger.info(f"  - 描述: {description}")
            
            # 如果您想测试完整的处理流程，可以取消下面的注释
            """
            # 测试完整的处理流程
            from datetime import datetime
            
            result = filter_handler.process_frame(
                frame=frame,
                frame_count=1,
                save_dir=Path("test_output"),
                camera_id="test_camera",
                sensor_data={},
                threshold=50,
                system_type="system_prompt_filter1",
                current_time=datetime.now()
            )
            
            coverage_float, analysis_result, alarm_status, is_abnormal, frame_path, suggestion, failure_types = result
            
            logger.info(f"完整处理结果:")
            logger.info(f"  - 覆盖率: {coverage_float}")
            logger.info(f"  - 警报状态: {alarm_status}")
            logger.info(f"  - 是否异常: {is_abnormal}")
            logger.info(f"  - 分析结果: {analysis_result}")
            logger.info(f"  - 建议: {suggestion}")
            logger.info(f"  - 故障类型: {failure_types}")
            """
            
        except Exception as e:
            logger.error(f"测试图片 {image_path} 时出现错误: {str(e)}")
            import traceback
            traceback.print_exc()

def test_precheck_only():
    """仅测试预检查功能，不依赖完整的处理流程"""
    
    mock_processor = MockProcessor()
    filter_handler = FilterHandler(mock_processor)
    
    # 测试图片路径
    test_image = "assets/frame_4010_2024_12_06_17_48_30.jpg"
    
    if not os.path.exists(test_image):
        logger.error(f"测试图片不存在: {test_image}")
        return
    
    logger.info(f"测试预检查功能，图片: {test_image}")
    
    try:
        # 读取图片
        frame = cv2.imread(test_image)
        if frame is None:
            logger.error(f"无法读取图片: {test_image}")
            return
        
        # 调用预检查方法
        is_suitable, description = filter_handler._check_filter_analysis_conditions(frame)
        
        logger.info(f"预检查结果:")
        logger.info(f"  - 是否满足分析条件: {is_suitable}")
        logger.info(f"  - 结果描述: {description}")
        
        if is_suitable:
            logger.info("✅ 图像满足滤池分析条件，可以继续进行YOLO和小模型识别")
        else:
            logger.info("❌ 图像不满足滤池分析条件，将跳过后续识别流程")
            
    except Exception as e:
        logger.error(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    logger.info("开始测试滤池预检查功能...")
    
    # 选择测试模式
    test_mode = "precheck_only"  # 可选: "precheck_only" 或 "full_test"
    
    if test_mode == "precheck_only":
        test_precheck_only()
    else:
        test_filter_precheck()
    
    logger.info("测试完成!")
